#!/usr/bin/env python3

import rclpy
from rclpy.node import Node
from nav2_system_tests.msg import EnvInfo

class TestEnvInfoNode(Node):
    def __init__(self):
        super().__init__('test_env_info')
        
        # 创建订阅者
        self.subscription = self.create_subscription(
            EnvInfo,
            'environment_info',
            self.env_info_callback,
            10)
        
        self.get_logger().info('Test EnvInfo node started, waiting for messages...')
    
    def env_info_callback(self, msg):
        self.get_logger().info(f'Received environment info:')
        self.get_logger().info(f'  Environment: {msg.environment}')
        self.get_logger().info(f'  Area ID: {msg.area_id}')
        self.get_logger().info(f'  Entrance ID: {msg.entrance_id}')
        self.get_logger().info(f'  Changed: {msg.changed}')

def main(args=None):
    rclpy.init(args=args)
    node = TestEnvInfoNode()
    
    try:
        rclpy.spin(node)
    except KeyboardInterrupt:
        pass
    finally:
        node.destroy_node()
        rclpy.shutdown()

if __name__ == '__main__':
    main()
