#!/usr/bin/env python3

import rclpy
from rclpy.node import Node
from std_msgs.msg import String
import json

class SimpleEnvMonitor(Node):
    def __init__(self):
        super().__init__('simple_env_monitor')
        
        # 创建发布者，使用标准的String消息类型
        self.env_pub = self.create_publisher(
            String,
            'environment_status',
            10)
        
        # 创建定时器，每秒发布一次环境状态
        self.timer = self.create_timer(1.0, self.publish_env_status)
        
        # 模拟环境状态
        self.environment = "outdoor"
        self.area_id = "building_1"
        self.entrance_id = "entrance_1_1"
        self.changed = False
        
        self.get_logger().info('Simple environment monitor started')
    
    def publish_env_status(self):
        # 创建JSON格式的环境状态消息
        env_data = {
            "environment": self.environment,
            "area_id": self.area_id,
            "entrance_id": self.entrance_id,
            "changed": self.changed
        }
        
        # 发布消息
        msg = String()
        msg.data = json.dumps(env_data)
        self.env_pub.publish(msg)
        
        # 重置changed标志
        self.changed = False

def main(args=None):
    rclpy.init(args=args)
    node = SimpleEnvMonitor()
    
    try:
        rclpy.spin(node)
    except KeyboardInterrupt:
        pass
    finally:
        node.destroy_node()
        rclpy.shutdown()

if __name__ == '__main__':
    main()
