#!/usr/bin/env python3

import sys
print("Python version:", sys.version)

try:
    import rclpy
    print("✓ rclpy imported successfully")
except ImportError as e:
    print("✗ Failed to import rclpy:", e)
    sys.exit(1)

try:
    from std_msgs.msg import String
    print("✓ std_msgs.msg.String imported successfully")
except ImportError as e:
    print("✗ Failed to import String:", e)
    sys.exit(1)

try:
    rclpy.init()
    print("✓ rclpy.init() successful")
except Exception as e:
    print("✗ rclpy.init() failed:", e)
    sys.exit(1)

try:
    from rclpy.node import Node
    print("✓ Node imported successfully")
    
    class TestNode(Node):
        def __init__(self):
            super().__init__('test_node')
            self.get_logger().info('Test node created successfully')
    
    node = TestNode()
    print("✓ Node created successfully")
    
    node.destroy_node()
    print("✓ Node destroyed successfully")
    
except Exception as e:
    print("✗ Node creation failed:", e)
    sys.exit(1)

try:
    rclpy.shutdown()
    print("✓ rclpy.shutdown() successful")
except Exception as e:
    print("✗ rclpy.shutdown() failed:", e)

print("All tests passed!")
