#!/usr/bin/env python3

import rclpy
from rclpy.node import Node
import sys
import signal

class SafeEnvListener(Node):
    def __init__(self):
        super().__init__('safe_env_listener')
        
        # 尝试导入消息类型
        try:
            from nav2_system_tests.msg import EnvInfo
            self.EnvInfo = EnvInfo
            self.get_logger().info('Successfully imported EnvInfo message type')
        except ImportError as e:
            self.get_logger().error(f'Failed to import EnvInfo: {e}')
            return
        
        # 创建订阅者
        try:
            self.subscription = self.create_subscription(
                self.EnvInfo,
                'environment_info',
                self.env_callback,
                10)
            self.get_logger().info('Successfully created subscription to /environment_info')
        except Exception as e:
            self.get_logger().error(f'Failed to create subscription: {e}')
            return
        
        self.message_count = 0
        
    def env_callback(self, msg):
        try:
            self.message_count += 1
            self.get_logger().info(f'Message #{self.message_count}:')
            self.get_logger().info(f'  Environment: {msg.environment}')
            self.get_logger().info(f'  Area ID: {msg.area_id}')
            self.get_logger().info(f'  Entrance ID: {msg.entrance_id}')
            self.get_logger().info(f'  Changed: {msg.changed}')
            self.get_logger().info('---')
            
            # 只显示前5条消息，然后退出
            if self.message_count >= 5:
                self.get_logger().info('Received 5 messages, shutting down...')
                rclpy.shutdown()
                
        except Exception as e:
            self.get_logger().error(f'Error in callback: {e}')

def signal_handler(sig, frame):
    print('\nShutting down gracefully...')
    rclpy.shutdown()
    sys.exit(0)

def main(args=None):
    # 设置信号处理器
    signal.signal(signal.SIGINT, signal_handler)
    
    try:
        rclpy.init(args=args)
        node = SafeEnvListener()
        
        print('Listening for environment info messages...')
        print('Press Ctrl+C to stop')
        
        rclpy.spin(node)
        
    except Exception as e:
        print(f'Error: {e}')
    finally:
        try:
            node.destroy_node()
        except:
            pass
        try:
            rclpy.shutdown()
        except:
            pass

if __name__ == '__main__':
    main()
